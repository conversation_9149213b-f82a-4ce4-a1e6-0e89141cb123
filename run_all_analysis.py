#!/usr/bin/env python3
"""
统一分析脚本运行器 - 将所有分析结果写入同一目录
"""

import os
import sys
import argparse
import subprocess
from datetime import datetime

def run_command(command, description):
    """运行命令并返回结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            return True
        else:
            print(f"❌ {description} 失败:")
            print(f"   错误信息: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="统一运行所有股票分析脚本")
    parser.add_argument("--input_dir", type=str, default="output", help="输入数据目录 (默认: output)")
    parser.add_argument("--output_dir", type=str, default="reports", help="输出报告目录 (默认: reports)")
    parser.add_argument("--api_key", type=str, help="Gemini API 密钥")
    
    args = parser.parse_args()
    
    print("🚀 统一股票分析工具")
    print("=" * 50)
    print(f"📁 输入目录: {args.input_dir}")
    print(f"📁 输出目录: {args.output_dir}")
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 错误: 输入目录 {args.input_dir} 不存在")
        sys.exit(1)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"📂 输出目录已创建: {args.output_dir}")
    
    # 检查API密钥
    api_key = args.api_key or os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("⚠️ 警告: 未设置 Gemini API 密钥，将跳过 Gemini 分析")
        run_gemini = False
    else:
        run_gemini = True
        os.environ['GEMINI_API_KEY'] = api_key
    
    print("\n" + "=" * 50)
    print("🔄 开始运行分析脚本...")
    print("=" * 50)
    
    results = []
    
    # 1. 运行增强版 Gemini 分析 (如果有API密钥)
    if run_gemini:
        cmd = f"python3 enhanced_gemini_analyzer.py --input_dir {args.input_dir} --output_dir {args.output_dir}"
        if args.api_key:
            cmd += f" --api_key {args.api_key}"
        success = run_command(cmd, "增强版 Gemini 分析")
        results.append(("增强版 Gemini 分析", success))
    else:
        print("⏭️ 跳过增强版 Gemini 分析 (无API密钥)")
        results.append(("增强版 Gemini 分析", "跳过"))
    
    print()
    
    # 2. 运行后排股票分析
    cmd = f"python3 bottom_stock_analyzer.py --input_dir {args.input_dir} --output_dir {args.output_dir}"
    success = run_command(cmd, "后排股票分析")
    results.append(("后排股票分析", success))
    
    print()
    
    # 3. 运行趋势分析
    cmd = f"python3 analyze_trends.py --output_dir {args.output_dir}"
    success = run_command(cmd, "趋势分析")
    results.append(("趋势分析", success))
    
    print("\n" + "=" * 50)
    print("📊 分析结果汇总")
    print("=" * 50)
    
    # 显示结果
    all_success = True
    for name, status in results:
        if status == "跳过":
            print(f"⏭️ {name}: 跳过")
        elif status:
            print(f"✅ {name}: 成功")
        else:
            print(f"❌ {name}: 失败")
            all_success = False
    
    print(f"\n📁 所有输出文件位置: {args.output_dir}")
    
    # 列出生成的文件
    if os.path.exists(args.output_dir):
        files = os.listdir(args.output_dir)
        if files:
            print(f"\n📄 生成的文件 ({len(files)} 个):")
            for file in sorted(files):
                file_path = os.path.join(args.output_dir, file)
                file_size = os.path.getsize(file_path)
                if file_size > 1024:
                    size_str = f"{file_size/1024:.1f}KB"
                else:
                    size_str = f"{file_size}B"
                print(f"  📄 {file} ({size_str})")
        else:
            print(f"\n⚠️ 输出目录为空")
    
    if all_success:
        print(f"\n🎉 所有分析完成! 结果已保存到 {args.output_dir}")
        return 0
    else:
        print(f"\n⚠️ 部分分析失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
