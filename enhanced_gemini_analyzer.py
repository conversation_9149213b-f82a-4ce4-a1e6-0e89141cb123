#!/usr/bin/env python3
"""
增强版 Gemini 股票分析器 - 强化提示词 + 后排股票上升概率分析
"""

import os
import json
import requests
import pandas as pd
from datetime import datetime
import argparse
from collections import defaultdict, Counter

class EnhancedGeminiAnalyzer:
    def __init__(self, api_key=None, output_dir="reports"):
        """初始化增强版分析器"""
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("请设置 GEMINI_API_KEY 环境变量或提供 API 密钥")

        self.output_dir = output_dir
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"📁 输出目录: {self.output_dir}")

        # 支持多个模型的回退机制
        self.models = [
            "gemini-1.5-flash",
            "gemini-1.5-pro",
            "gemini-pro",
            "gemini-1.0-pro"
        ]
        self.base_url_template = "https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
    
    def load_stock_data(self, input_dir):
        """加载股票数据"""
        print("📊 正在加载股票数据...")
        
        csv_files = []
        for date_folder in sorted(os.listdir(input_dir)):
            date_folder_path = os.path.join(input_dir, date_folder)
            if os.path.isdir(date_folder_path):
                csv_file_path = os.path.join(date_folder_path, "above_cloud_daily_change.csv")
                if os.path.isfile(csv_file_path):
                    csv_files.append((date_folder, csv_file_path))
        
        all_data = {}
        for date_str, file_path in csv_files:
            try:
                df = pd.read_csv(file_path, dtype={'股票代码': str})
                df['排名'] = range(1, len(df) + 1)
                all_data[date_str] = df
                print(f"  ✅ {date_str}: {len(df)} 只股票")
            except Exception as e:
                print(f"  ❌ {date_str}: 加载失败 - {e}")
        
        return all_data
    
    def extract_rising_stocks(self, all_data):
        """提取排名上升股票"""
        print("🔍 正在分析排名上升股票...")
        
        dates = sorted(all_data.keys())
        rising_stocks = []
        
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            
            current_df = all_data[current_date]
            next_df = all_data[next_date]
            
            current_df['股票代码'] = current_df['股票代码'].astype(str)
            next_df['股票代码'] = next_df['股票代码'].astype(str)
            
            common_stocks = set(current_df['股票代码']) & set(next_df['股票代码'])
            
            for stock_code in common_stocks:
                current_rank = current_df[current_df['股票代码'] == stock_code]['排名'].iloc[0]
                next_rank = next_df[next_df['股票代码'] == stock_code]['排名'].iloc[0]
                
                if next_rank < current_rank:
                    stock_info = next_df[next_df['股票代码'] == stock_code].iloc[0]
                    
                    rising_stocks.append({
                        'stock_code': stock_code,
                        'stock_name': stock_info.get('股票名称', '未知'),
                        'start_date': current_date,
                        'end_date': next_date,
                        'start_rank': current_rank,
                        'end_rank': next_rank,
                        'rank_improvement': current_rank - next_rank,
                        'total_gain': stock_info.get('当日涨幅(%)', 0),
                        'sector': stock_info.get('申万板块', '未知板块'),
                        'concepts': stock_info.get('开盘啦概念', ''),
                        'market_cap': stock_info.get('流通市值', 0),
                        'volume_ratio': stock_info.get('量比', 1),
                        'turnover_rate': stock_info.get('换手率(%)', 0)
                    })
        
        rising_stocks.sort(key=lambda x: x['total_gain'], reverse=True)
        print(f"  📈 发现 {len(rising_stocks)} 只排名上升股票")
        
        return rising_stocks
    
    def analyze_bottom_stocks_potential(self, all_data):
        """分析后排股票的上升潜力"""
        print("🔮 正在分析后排股票上升潜力...")
        
        dates = sorted(all_data.keys())
        latest_date = dates[-1]
        latest_df = all_data[latest_date]
        
        # 定义后排股票（排名在后50%）
        total_stocks = len(latest_df)
        bottom_threshold = total_stocks // 2
        bottom_stocks = latest_df[latest_df['排名'] > bottom_threshold].copy()
        
        print(f"  📊 后排股票定义: 排名 > {bottom_threshold} (共{len(bottom_stocks)}只)")
        
        # 分析历史上升模式
        historical_patterns = self.analyze_historical_rising_patterns(all_data)
        
        # 为每只后排股票计算上升概率
        potential_stocks = []
        
        for _, stock in bottom_stocks.iterrows():
            stock_code = str(stock['股票代码'])
            
            # 计算上升概率得分
            probability_score = self.calculate_rising_probability(
                stock, historical_patterns, all_data
            )
            
            potential_stocks.append({
                'stock_code': stock_code,
                'stock_name': stock.get('股票名称', '未知'),
                'current_rank': stock['排名'],
                'sector': stock.get('申万板块', '未知板块'),
                'concepts': stock.get('开盘啦概念', ''),
                'market_cap': stock.get('流通市值', 0),
                'current_gain': stock.get('当日涨幅(%)', 0),
                'volume_ratio': stock.get('量比', 1),
                'turnover_rate': stock.get('换手率(%)', 0),
                'probability_score': probability_score['total_score'],
                'score_breakdown': probability_score
            })
        
        # 按概率得分排序
        potential_stocks.sort(key=lambda x: x['probability_score'], reverse=True)
        
        print(f"  🎯 完成后排股票潜力分析，发现 {len(potential_stocks)} 只候选股票")
        
        return potential_stocks[:20]  # 返回前20只最有潜力的
    
    def analyze_historical_rising_patterns(self, all_data):
        """分析历史上升模式"""
        dates = sorted(all_data.keys())
        patterns = {
            'sector_success_rate': defaultdict(list),
            'concept_success_rate': defaultdict(list),
            'market_cap_patterns': [],
            'volume_patterns': [],
            'gain_patterns': []
        }
        
        # 分析历史数据中的上升模式
        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]
            
            current_df = all_data[current_date]
            next_df = all_data[next_date]
            
            current_df['股票代码'] = current_df['股票代码'].astype(str)
            next_df['股票代码'] = next_df['股票代码'].astype(str)
            
            common_stocks = set(current_df['股票代码']) & set(next_df['股票代码'])
            
            for stock_code in common_stocks:
                current_stock = current_df[current_df['股票代码'] == stock_code].iloc[0]
                next_stock = next_df[next_df['股票代码'] == stock_code].iloc[0]
                
                current_rank = current_stock['排名']
                next_rank = next_stock['排名']
                
                # 记录是否上升
                is_rising = next_rank < current_rank
                
                # 板块成功率
                sector = current_stock.get('申万板块', '未知')
                patterns['sector_success_rate'][sector].append(is_rising)
                
                # 概念成功率
                concepts = current_stock.get('开盘啦概念', '')
                if concepts and pd.notna(concepts):
                    for concept in concepts.split(','):
                        concept = concept.strip()
                        if concept:
                            patterns['concept_success_rate'][concept].append(is_rising)
                
                # 市值、量比、涨幅模式
                if is_rising:
                    patterns['market_cap_patterns'].append(current_stock.get('流通市值', 0))
                    patterns['volume_patterns'].append(current_stock.get('量比', 1))
                    patterns['gain_patterns'].append(current_stock.get('当日涨幅(%)', 0))
        
        return patterns
    
    def calculate_rising_probability(self, stock, patterns, all_data):
        """计算股票上升概率"""
        score_breakdown = {
            'sector_score': 0,
            'concept_score': 0,
            'market_cap_score': 0,
            'volume_score': 0,
            'gain_score': 0,
            'technical_score': 0
        }
        
        # 1. 板块得分
        sector = stock.get('申万板块', '未知')
        if sector in patterns['sector_success_rate']:
            success_rate = sum(patterns['sector_success_rate'][sector]) / len(patterns['sector_success_rate'][sector])
            score_breakdown['sector_score'] = success_rate * 20
        
        # 2. 概念得分
        concepts = stock.get('开盘啦概念', '')
        if concepts and pd.notna(concepts):
            concept_scores = []
            for concept in concepts.split(','):
                concept = concept.strip()
                if concept in patterns['concept_success_rate']:
                    success_rate = sum(patterns['concept_success_rate'][concept]) / len(patterns['concept_success_rate'][concept])
                    concept_scores.append(success_rate)
            if concept_scores:
                score_breakdown['concept_score'] = max(concept_scores) * 25
        
        # 3. 市值得分（小盘股更容易上升）
        market_cap = stock.get('流通市值', 0)
        if market_cap and pd.notna(market_cap):
            if float(market_cap) < 50:
                score_breakdown['market_cap_score'] = 15
            elif float(market_cap) < 100:
                score_breakdown['market_cap_score'] = 10
            elif float(market_cap) < 200:
                score_breakdown['market_cap_score'] = 5
        
        # 4. 量比得分（放量更容易上升）
        volume_ratio = stock.get('量比', 1)
        if volume_ratio and pd.notna(volume_ratio):
            if float(volume_ratio) > 2:
                score_breakdown['volume_score'] = 15
            elif float(volume_ratio) > 1.5:
                score_breakdown['volume_score'] = 10
            elif float(volume_ratio) > 1.2:
                score_breakdown['volume_score'] = 5
        
        # 5. 涨幅得分（适度涨幅更健康）
        current_gain = stock.get('当日涨幅(%)', 0)
        if current_gain and pd.notna(current_gain):
            gain = float(current_gain)
            if 0 < gain <= 3:
                score_breakdown['gain_score'] = 10
            elif 3 < gain <= 6:
                score_breakdown['gain_score'] = 8
            elif gain > 6:
                score_breakdown['gain_score'] = 5
            elif -2 <= gain <= 0:
                score_breakdown['gain_score'] = 6
        
        # 6. 技术面得分（换手率）
        turnover_rate = stock.get('换手率(%)', 0)
        if turnover_rate and pd.notna(turnover_rate):
            if 2 <= float(turnover_rate) <= 8:
                score_breakdown['technical_score'] = 10
            elif 1 <= float(turnover_rate) < 2:
                score_breakdown['technical_score'] = 6
            elif 8 < float(turnover_rate) <= 15:
                score_breakdown['technical_score'] = 8
        
        # 计算总分
        total_score = sum(score_breakdown.values())
        score_breakdown['total_score'] = total_score
        
        return score_breakdown
    
    def create_enhanced_prompt(self, rising_data, potential_data):
        """创建增强版分析提示词"""
        prompt = f"""
你是一位顶级的量化分析师和投资策略专家，拥有20年A股市场实战经验。请基于以下数据进行深度分析：

## 📊 **已上升股票数据**
{rising_data}

## 🔮 **后排潜力股票数据**  
{potential_data}

请从以下维度进行专业分析，要求具体、实用、可操作：

## 🎯 **核心发现与投资机会**

### 1. **已上升股票深度解析**
- **超级强势特征**: 涨幅>15%股票的共同DNA是什么？
- **板块轮动规律**: 哪些板块正在接力上涨？下一个热点板块是什么？
- **概念爆发逻辑**: 当前最强概念的持续性和扩散性如何？
- **资金流向分析**: 大资金偏好什么类型的标的？

### 2. **后排潜力股挖掘**
- **黑马识别**: 在后排股票中，哪些最有可能成为下一批黑马？
- **上升概率排序**: 按上升概率给出TOP10潜力股，并说明理由
- **介入时机**: 什么时候是最佳介入时机？
- **风险评估**: 每只潜力股的主要风险点是什么？

## 📈 **量化投资策略**

### 3. **选股模型优化**
- **多因子模型**: 基于数据构建选股因子权重（板块30%+概念25%+市值20%+技术15%+基本面10%）
- **概率预测**: 给出未来3-5个交易日排名上升概率>70%的股票清单
- **组合构建**: 推荐一个10只股票的投资组合，包含3只已上升股票+7只潜力股

### 4. **操作策略细化**
- **分批建仓**: 具体的分批买入策略（时间点、仓位比例）
- **止盈止损**: 每个类型股票的具体止盈止损点位
- **仓位管理**: 不同风险偏好下的仓位配置建议
- **风控措施**: 5个关键风控指标和预警信号

## 🔥 **市场前瞻与布局**

### 5. **趋势预判**
- **短期热点**: 未来1-2周最可能爆发的板块和概念
- **中期主线**: 未来1-2个月的主要投资主线
- **资金轮动**: 预测资金从哪些板块流出，流入哪些板块

### 6. **实战建议**
- **今日操作**: 如果今天收盘前操作，重点关注哪3只股票？
- **明日策略**: 明天开盘后的具体操作计划
- **本周布局**: 本周重点布局的方向和标的

## ⚠️ **风险管理与应对**

### 7. **风险识别**
- **系统性风险**: 当前市场面临的主要系统性风险
- **个股风险**: 重点关注股票的特定风险点
- **流动性风险**: 哪些股票可能面临流动性问题

### 8. **应急预案**
- **市场下跌**: 如果市场突然下跌5%以上的应对策略
- **个股暴跌**: 持仓股票暴跌时的处理原则
- **黑天鹅事件**: 面对突发事件的资产保护措施

## 💎 **特别要求**

1. **数据驱动**: 所有结论必须基于提供的数据，避免主观臆断
2. **量化指标**: 给出具体的数值指标，如概率、比例、点位等
3. **可操作性**: 每个建议都要具体到股票代码、买入价位、仓位比例
4. **风险提示**: 每个推荐都要明确风险等级（低/中/高）
5. **时效性**: 重点关注短期（1-5个交易日）的操作机会

请用专业、精准的语言进行分析，重点突出实战价值和可操作性。使用表格、列表等结构化方式组织信息，让分析结果更清晰易懂。
"""
        return prompt
    
    def format_analysis_data(self, rising_stocks, potential_stocks):
        """格式化分析数据"""
        # 格式化已上升股票数据
        rising_data = {
            "总体统计": {
                "排名上升股票数量": len(rising_stocks),
                "平均涨幅": round(sum(stock['total_gain'] for stock in rising_stocks) / len(rising_stocks), 2),
                "最大涨幅": max(stock['total_gain'] for stock in rising_stocks),
                "最小涨幅": min(stock['total_gain'] for stock in rising_stocks),
                "上涨股票数": len([s for s in rising_stocks if s['total_gain'] > 0])
            },
            "前10名详情": [
                {
                    "排名": i,
                    "股票代码": stock['stock_code'],
                    "股票名称": stock['stock_name'],
                    "申万板块": stock['sector'],
                    "概念标签": stock['concepts'],
                    "流通市值": f"{float(stock['market_cap']):.1f}亿" if stock['market_cap'] else "未知",
                    "排名变化": f"从{stock['start_rank']}→{stock['end_rank']} (上升{stock['rank_improvement']}位)",
                    "涨幅": f"{stock['total_gain']:.2f}%",
                    "量比": stock.get('volume_ratio', 'N/A'),
                    "换手率": f"{stock.get('turnover_rate', 0):.2f}%"
                }
                for i, stock in enumerate(rising_stocks[:10], 1)
            ]
        }
        
        # 格式化潜力股票数据
        potential_data = {
            "分析说明": "基于历史模式分析的后排股票上升概率预测",
            "评分标准": "总分100分 = 板块20分 + 概念25分 + 市值15分 + 量比15分 + 涨幅10分 + 技术15分",
            "前20名潜力股": [
                {
                    "排名": i,
                    "股票代码": stock['stock_code'],
                    "股票名称": stock['stock_name'],
                    "当前排名": stock['current_rank'],
                    "申万板块": stock['sector'],
                    "概念标签": stock['concepts'],
                    "流通市值": f"{float(stock['market_cap']):.1f}亿" if stock['market_cap'] else "未知",
                    "当日涨幅": f"{stock['current_gain']:.2f}%",
                    "量比": stock.get('volume_ratio', 'N/A'),
                    "换手率": f"{stock.get('turnover_rate', 0):.2f}%",
                    "上升概率得分": f"{stock['probability_score']:.1f}分",
                    "得分详情": {
                        "板块得分": f"{stock['score_breakdown']['sector_score']:.1f}",
                        "概念得分": f"{stock['score_breakdown']['concept_score']:.1f}",
                        "市值得分": f"{stock['score_breakdown']['market_cap_score']:.1f}",
                        "量比得分": f"{stock['score_breakdown']['volume_score']:.1f}",
                        "涨幅得分": f"{stock['score_breakdown']['gain_score']:.1f}",
                        "技术得分": f"{stock['score_breakdown']['technical_score']:.1f}"
                    }
                }
                for i, stock in enumerate(potential_stocks, 1)
            ]
        }
        
        return json.dumps(rising_data, ensure_ascii=False, indent=2), json.dumps(potential_data, ensure_ascii=False, indent=2)
    
    def call_gemini_api(self, prompt):
        """调用 Gemini API"""
        print("🤖 正在调用增强版 Gemini API...")
        
        headers = {'Content-Type': 'application/json'}
        
        data = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.8,  # 提高创造性
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 4096,  # 增加输出长度
            }
        }
        
        for i, model in enumerate(self.models):
            try:
                url = self.base_url_template.format(model=model)
                print(f"  🔄 尝试模型: {model}")
                
                response = requests.post(f"{url}?key={self.api_key}", headers=headers, json=data, timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and len(result['candidates']) > 0:
                        print(f"  ✅ 模型 {model} 调用成功!")
                        return result['candidates'][0]['content']['parts'][0]['text']
                    else:
                        print(f"  ⚠️ 模型 {model} 返回空结果")
                        continue
                elif response.status_code == 404:
                    print(f"  ❌ 模型 {model} 不可用 (404)")
                    continue
                else:
                    print(f"  ❌ 模型 {model} 调用失败: {response.status_code}")
                    continue
                    
            except Exception as e:
                print(f"  ❌ 模型 {model} 异常: {str(e)}")
                continue
        
        return "所有模型调用失败，请检查网络连接和API密钥"
    
    def analyze(self, input_dir):
        """执行完整的增强分析"""
        print("🚀 开始增强版股票趋势 AI 分析")
        print("=" * 60)
        
        # 1. 加载数据
        all_data = self.load_stock_data(input_dir)
        if not all_data:
            print("❌ 没有找到有效的股票数据")
            return
        
        # 2. 分析已上升股票
        rising_stocks = self.extract_rising_stocks(all_data)
        if not rising_stocks:
            print("❌ 没有发现排名上升的股票")
            return
        
        # 3. 分析后排潜力股票
        potential_stocks = self.analyze_bottom_stocks_potential(all_data)
        
        # 4. 格式化数据
        rising_data_str, potential_data_str = self.format_analysis_data(rising_stocks, potential_stocks)
        
        # 5. 创建增强提示词
        prompt = self.create_enhanced_prompt(rising_data_str, potential_data_str)
        
        # 6. 调用 AI 分析
        analysis_result = self.call_gemini_api(prompt)
        
        # 7. 输出结果
        print("\n" + "=" * 60)
        print("🎯 增强版 Gemini AI 分析结果")
        print("=" * 60)
        print(analysis_result)
        
        # 8. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(self.output_dir, f"enhanced_gemini_analysis_{timestamp}.md")

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 增强版股票趋势 AI 分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**数据源**: {input_dir}\n\n")
            f.write(f"**已上升股票数**: {len(rising_stocks)}\n\n")
            f.write(f"**潜力股票数**: {len(potential_stocks)}\n\n")
            f.write("---\n\n")
            f.write(analysis_result)

        print(f"\n📄 增强分析报告已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="增强版 Gemini AI 股票趋势分析")
    parser.add_argument("--input_dir", type=str, required=True, help="包含股票数据的目录路径")
    parser.add_argument("--output_dir", type=str, default="reports", help="输出目录路径 (默认: reports)")
    parser.add_argument("--api_key", type=str, help="Gemini API 密钥")

    args = parser.parse_args()

    try:
        analyzer = EnhancedGeminiAnalyzer(api_key=args.api_key, output_dir=args.output_dir)
        analyzer.analyze(args.input_dir)
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()

# 添加独立的后排股票分析功能
class BottomStockAnalyzer:
    """专门分析后排股票上升潜力的工具"""

    def __init__(self, input_dir):
        self.input_dir = input_dir
        self.all_data = self.load_data()

    def load_data(self):
        """加载数据"""
        all_data = {}
        for date_folder in sorted(os.listdir(self.input_dir)):
            date_folder_path = os.path.join(self.input_dir, date_folder)
            if os.path.isdir(date_folder_path):
                csv_file_path = os.path.join(date_folder_path, "above_cloud_daily_change.csv")
                if os.path.isfile(csv_file_path):
                    try:
                        df = pd.read_csv(csv_file_path, dtype={'股票代码': str})
                        df['排名'] = range(1, len(df) + 1)
                        all_data[date_folder] = df
                    except Exception as e:
                        print(f"加载 {date_folder} 失败: {e}")
        return all_data

    def analyze_rising_success_factors(self):
        """分析历史上升成功因子"""
        print("📊 分析历史上升成功因子...")

        dates = sorted(self.all_data.keys())
        success_factors = {
            'sector_performance': defaultdict(list),
            'concept_performance': defaultdict(list),
            'market_cap_ranges': {'small': [], 'medium': [], 'large': []},
            'volume_patterns': [],
            'gain_patterns': [],
            'rank_jump_patterns': []
        }

        for i in range(len(dates) - 1):
            current_date = dates[i]
            next_date = dates[i + 1]

            current_df = self.all_data[current_date]
            next_df = self.all_data[next_date]

            # 找到后排股票（排名后50%）
            total_stocks = len(current_df)
            bottom_threshold = total_stocks // 2
            bottom_stocks = current_df[current_df['排名'] > bottom_threshold]

            for _, stock in bottom_stocks.iterrows():
                stock_code = str(stock['股票代码'])
                current_rank = stock['排名']

                # 查找次日排名
                next_stock = next_df[next_df['股票代码'] == stock_code]
                if not next_stock.empty:
                    next_rank = next_stock.iloc[0]['排名']
                    rank_improvement = current_rank - next_rank

                    # 记录成功上升的因子
                    if rank_improvement > 10:  # 排名上升超过10位
                        # 板块因子
                        sector = stock.get('申万板块', '未知')
                        success_factors['sector_performance'][sector].append(rank_improvement)

                        # 概念因子
                        concepts = stock.get('开盘啦概念', '')
                        if concepts and pd.notna(concepts):
                            for concept in concepts.split(','):
                                concept = concept.strip()
                                if concept:
                                    success_factors['concept_performance'][concept].append(rank_improvement)

                        # 市值因子
                        market_cap = stock.get('流通市值', 0)
                        if market_cap and pd.notna(market_cap):
                            cap = float(market_cap)
                            if cap < 50:
                                success_factors['market_cap_ranges']['small'].append(rank_improvement)
                            elif cap < 200:
                                success_factors['market_cap_ranges']['medium'].append(rank_improvement)
                            else:
                                success_factors['market_cap_ranges']['large'].append(rank_improvement)

                        # 量比和涨幅因子
                        volume_ratio = stock.get('量比', 1)
                        if volume_ratio and pd.notna(volume_ratio):
                            success_factors['volume_patterns'].append(float(volume_ratio))

                        current_gain = stock.get('当日涨幅(%)', 0)
                        if current_gain and pd.notna(current_gain):
                            success_factors['gain_patterns'].append(float(current_gain))

                        success_factors['rank_jump_patterns'].append(rank_improvement)

        return success_factors

    def generate_bottom_stock_report(self):
        """生成后排股票分析报告"""
        print("📝 生成后排股票潜力分析报告...")

        # 分析成功因子
        success_factors = self.analyze_rising_success_factors()

        # 获取最新数据
        latest_date = max(self.all_data.keys())
        latest_df = self.all_data[latest_date]

        # 定义后排股票
        total_stocks = len(latest_df)
        bottom_threshold = total_stocks // 2
        bottom_stocks = latest_df[latest_df['排名'] > bottom_threshold].copy()

        # 生成报告
        report = f"""
# 🔮 后排股票上升潜力深度分析报告

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**数据源**: {self.input_dir}
**分析范围**: 排名 > {bottom_threshold} 的后排股票 (共{len(bottom_stocks)}只)

## 📊 历史成功因子分析

### 🏆 最成功的板块 (平均排名上升幅度)
"""

        # 板块成功率分析
        sector_avg = {}
        for sector, improvements in success_factors['sector_performance'].items():
            if len(improvements) >= 3:  # 至少3次成功记录
                avg_improvement = sum(improvements) / len(improvements)
                sector_avg[sector] = {
                    'avg_improvement': avg_improvement,
                    'success_count': len(improvements)
                }

        # 按平均上升幅度排序
        sorted_sectors = sorted(sector_avg.items(), key=lambda x: x[1]['avg_improvement'], reverse=True)

        for i, (sector, data) in enumerate(sorted_sectors[:10], 1):
            report += f"{i}. **{sector}**: 平均上升 {data['avg_improvement']:.1f} 位 ({data['success_count']}次)\n"

        # 概念成功率分析
        report += "\n### 🎯 最热门的概念 (平均排名上升幅度)\n"

        concept_avg = {}
        for concept, improvements in success_factors['concept_performance'].items():
            if len(improvements) >= 2:  # 至少2次成功记录
                avg_improvement = sum(improvements) / len(improvements)
                concept_avg[concept] = {
                    'avg_improvement': avg_improvement,
                    'success_count': len(improvements)
                }

        sorted_concepts = sorted(concept_avg.items(), key=lambda x: x[1]['avg_improvement'], reverse=True)

        for i, (concept, data) in enumerate(sorted_concepts[:15], 1):
            report += f"{i}. **{concept}**: 平均上升 {data['avg_improvement']:.1f} 位 ({data['success_count']}次)\n"

        # 市值分析
        report += "\n### 💎 市值效应分析\n"
        for cap_range, improvements in success_factors['market_cap_ranges'].items():
            if improvements:
                avg_improvement = sum(improvements) / len(improvements)
                range_name = {'small': '小盘股(<50亿)', 'medium': '中盘股(50-200亿)', 'large': '大盘股(>200亿)'}
                report += f"- **{range_name[cap_range]}**: 平均上升 {avg_improvement:.1f} 位 ({len(improvements)}次)\n"

        # 技术指标分析
        if success_factors['volume_patterns']:
            avg_volume = sum(success_factors['volume_patterns']) / len(success_factors['volume_patterns'])
            report += f"\n### 📈 技术指标特征\n"
            report += f"- **成功股票平均量比**: {avg_volume:.2f}\n"

        if success_factors['gain_patterns']:
            avg_gain = sum(success_factors['gain_patterns']) / len(success_factors['gain_patterns'])
            report += f"- **成功股票平均当日涨幅**: {avg_gain:.2f}%\n"

        if success_factors['rank_jump_patterns']:
            avg_jump = sum(success_factors['rank_jump_patterns']) / len(success_factors['rank_jump_patterns'])
            max_jump = max(success_factors['rank_jump_patterns'])
            report += f"- **平均排名上升幅度**: {avg_jump:.1f} 位\n"
            report += f"- **最大排名上升幅度**: {max_jump} 位\n"

        # 当前后排股票分析
        report += f"\n## 🎯 当前后排股票潜力分析 ({latest_date})\n\n"

        # 按板块分组分析
        sector_groups = bottom_stocks.groupby('申万板块')

        report += "### 📊 按板块分组的潜力股票\n\n"

        for sector, group in sector_groups:
            if sector in sector_avg:
                success_data = sector_avg[sector]
                report += f"#### 🔥 {sector} (历史平均上升{success_data['avg_improvement']:.1f}位)\n\n"

                # 选择该板块最有潜力的股票
                sector_stocks = []
                for _, stock in group.iterrows():
                    # 计算潜力得分
                    score = 0

                    # 量比得分
                    volume_ratio = stock.get('量比', 1)
                    if volume_ratio and pd.notna(volume_ratio) and float(volume_ratio) > 1.5:
                        score += 20

                    # 市值得分
                    market_cap = stock.get('流通市值', 0)
                    if market_cap and pd.notna(market_cap) and float(market_cap) < 100:
                        score += 15

                    # 涨幅得分
                    current_gain = stock.get('当日涨幅(%)', 0)
                    if current_gain and pd.notna(current_gain):
                        gain = float(current_gain)
                        if 0 <= gain <= 5:
                            score += 10

                    # 概念加分
                    concepts = stock.get('开盘啦概念', '')
                    if concepts and pd.notna(concepts):
                        for concept in concepts.split(','):
                            concept = concept.strip()
                            if concept in concept_avg:
                                score += 5
                                break

                    sector_stocks.append({
                        'stock': stock,
                        'score': score
                    })

                # 按得分排序，显示前5只
                sector_stocks.sort(key=lambda x: x['score'], reverse=True)

                for i, item in enumerate(sector_stocks[:5], 1):
                    stock = item['stock']
                    score = item['score']

                    market_cap = stock.get('流通市值', 0)
                    market_cap_str = f"{float(market_cap):.1f}亿" if market_cap and pd.notna(market_cap) else "未知"

                    volume_ratio = stock.get('量比', 1)
                    volume_str = f"{float(volume_ratio):.2f}" if volume_ratio and pd.notna(volume_ratio) else "N/A"

                    current_gain = stock.get('当日涨幅(%)', 0)
                    gain_str = f"{float(current_gain):.2f}%" if current_gain and pd.notna(current_gain) else "N/A"

                    concepts = stock.get('开盘啦概念', '')
                    concept_str = concepts if concepts and pd.notna(concepts) else "无特殊概念"

                    report += f"{i}. **{stock['股票代码']} {stock.get('股票名称', '未知')}** (潜力得分: {score})\n"
                    report += f"   - 当前排名: {stock['排名']}\n"
                    report += f"   - 流通市值: {market_cap_str}\n"
                    report += f"   - 量比: {volume_str}\n"
                    report += f"   - 当日涨幅: {gain_str}\n"
                    report += f"   - 概念: {concept_str}\n\n"

        # 投资建议
        report += """
## 💡 投资策略建议

### 🎯 选股策略
1. **优先选择历史成功率高的板块**
2. **关注小盘股（市值<100亿）**
3. **寻找量比>1.5的放量股票**
4. **避免当日涨幅过大的股票**
5. **重点关注热门概念股**

### ⚠️ 风险提示
1. **后排股票波动性较大，注意仓位控制**
2. **部分股票可能存在基本面问题**
3. **市场情绪变化可能影响上升概率**
4. **建议分散投资，不要集中持仓**

### 📅 操作建议
1. **分批建仓**: 分2-3次建仓，每次1/3仓位
2. **止损设置**: 跌破买入价8%坚决止损
3. **止盈策略**: 上涨15%以上分批止盈
4. **持仓周期**: 建议持仓周期3-10个交易日
"""

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"bottom_stocks_analysis_{timestamp}.md"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📄 后排股票分析报告已保存到: {filename}")
        return filename

# 独立运行后排股票分析的函数
def analyze_bottom_stocks_only(input_dir):
    """独立分析后排股票"""
    analyzer = BottomStockAnalyzer(input_dir)
    return analyzer.generate_bottom_stock_report()
