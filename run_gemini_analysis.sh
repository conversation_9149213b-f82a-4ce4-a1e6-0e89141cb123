#!/bin/bash

# Gemini API 股票分析运行脚本
# 使用方法: ./run_gemini_analysis.sh [API_KEY] [OUTPUT_DIR]

echo "🚀 Gemini API 股票分析工具"
echo "=================================="

# 设置默认输出目录
OUTPUT_DIR="reports"
if [ $# -ge 2 ]; then
    OUTPUT_DIR="$2"
    echo "📁 使用指定输出目录: $OUTPUT_DIR"
else
    echo "📁 使用默认输出目录: $OUTPUT_DIR"
fi

# 检查参数
if [ $# -ge 1 ] && [ "$1" != "" ]; then
    export GEMINI_API_KEY="$1"
    echo "🔑 使用提供的 API 密钥"
elif [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ 错误: 请提供 API 密钥"
    echo ""
    echo "使用方法:"
    echo "  方法1: ./run_gemini_analysis.sh YOUR_API_KEY [OUTPUT_DIR]"
    echo "  方法2: export GEMINI_API_KEY=YOUR_API_KEY && ./run_gemini_analysis.sh [OUTPUT_DIR]"
    echo ""
    echo "💡 获取 API 密钥: https://makersuite.google.com/app/apikey"
    exit 1
else
    echo "🔑 使用环境变量中的 API 密钥"
fi

# 检查 Python 环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到 Python3"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import requests, pandas" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少依赖，正在安装..."
    pip3 install requests pandas
fi

# 检查数据目录
if [ ! -d "output" ]; then
    echo "❌ 错误: 未找到 output 目录"
    echo "💡 请确保在正确的项目目录中运行此脚本"
    exit 1
fi

# 测试 API 连接
echo "🧪 测试 API 连接..."
python3 test_gemini_api.py
if [ $? -ne 0 ]; then
    echo "❌ API 测试失败，请检查密钥和网络连接"
    exit 1
fi

echo ""
echo "✅ API 测试成功，开始分析..."
echo ""

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo ""
echo "🔄 开始运行所有分析脚本..."
echo ""

# 1. 运行增强版 Gemini 分析
echo "1️⃣ 运行增强版 Gemini 分析..."
python3 enhanced_gemini_analyzer.py --input_dir output --output_dir "$OUTPUT_DIR"
ENHANCED_STATUS=$?

echo ""

# 2. 运行后排股票分析
echo "2️⃣ 运行后排股票分析..."
python3 bottom_stock_analyzer.py --input_dir output --output_dir "$OUTPUT_DIR"
BOTTOM_STATUS=$?

echo ""

# 3. 运行趋势分析
echo "3️⃣ 运行趋势分析..."
python3 analyze_trends.py --output_dir "$OUTPUT_DIR"
TRENDS_STATUS=$?

echo ""

# 检查结果
if [ $ENHANCED_STATUS -eq 0 ] && [ $BOTTOM_STATUS -eq 0 ] && [ $TRENDS_STATUS -eq 0 ]; then
    echo "🎉 所有分析完成!"
    echo "📁 所有报告已保存到: $OUTPUT_DIR"
    echo ""
    echo "📄 生成的报告文件:"
    ls -la "$OUTPUT_DIR"/*.md 2>/dev/null | while read line; do
        echo "  $line"
    done
    echo ""
    echo "📊 生成的数据文件:"
    ls -la "$OUTPUT_DIR"/*.json 2>/dev/null | while read line; do
        echo "  $line"
    done
else
    echo "❌ 部分分析失败"
    echo "  增强版 Gemini 分析: $([ $ENHANCED_STATUS -eq 0 ] && echo "✅ 成功" || echo "❌ 失败")"
    echo "  后排股票分析: $([ $BOTTOM_STATUS -eq 0 ] && echo "✅ 成功" || echo "❌ 失败")"
    echo "  趋势分析: $([ $TRENDS_STATUS -eq 0 ] && echo "✅ 成功" || echo "❌ 失败")"
    exit 1
fi
