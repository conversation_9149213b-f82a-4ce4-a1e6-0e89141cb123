# 📁 统一输出目录指南

## 概述

所有分析脚本现在都将结果写入统一的输出目录，默认为 `reports/`，避免了之前分散在不同目录的问题。

## 🎯 主要改进

### 之前的问题
- `enhanced_gemini_analyzer.py` 写入 `analysis_reports/`
- `bottom_stock_analyzer.py` 写入 `analysis_reports/` 但有时也写入 `my_analysis/` 或 `test_reports/`
- `analyze_trends.py` 没有文件输出功能
- 输出文件分散，难以管理

### 现在的解决方案
- ✅ 所有脚本统一使用 `reports/` 作为默认输出目录
- ✅ 所有脚本都支持 `--output_dir` 参数自定义输出目录
- ✅ `analyze_trends.py` 新增文件输出功能
- ✅ 提供统一的运行脚本

## 📂 输出目录结构

```
reports/
├── enhanced_gemini_analysis_YYYYMMDD_HHMMSS.md    # 增强版 Gemini 分析报告
├── bottom_stocks_analysis_YYYYMMDD_HHMMSS.md      # 后排股票分析报告
├── bottom_stocks_data_YYYYMMDD_HHMMSS.json        # 后排股票数据文件
└── trends_analysis_YYYYMMDD_HHMMSS.md             # 趋势分析报告
```

## 🚀 使用方法

### 方法1: 使用统一运行脚本 (推荐)

```bash
# 使用默认输出目录 (reports/)
python3 run_all_analysis.py --input_dir output --api_key YOUR_API_KEY

# 自定义输出目录
python3 run_all_analysis.py --input_dir output --output_dir my_reports --api_key YOUR_API_KEY

# 不运行 Gemini 分析 (无API密钥)
python3 run_all_analysis.py --input_dir output --output_dir my_reports
```

### 方法2: 使用更新的 Shell 脚本

```bash
# 使用默认输出目录
./run_gemini_analysis.sh YOUR_API_KEY

# 自定义输出目录
./run_gemini_analysis.sh YOUR_API_KEY my_reports
```

### 方法3: 单独运行各个脚本

```bash
# 增强版 Gemini 分析
python3 enhanced_gemini_analyzer.py --input_dir output --output_dir reports --api_key YOUR_API_KEY

# 后排股票分析
python3 bottom_stock_analyzer.py --input_dir output --output_dir reports

# 趋势分析
python3 analyze_trends.py --output_dir reports
```

## 📊 输出文件说明

### 1. 增强版 Gemini 分析报告
- **文件名**: `enhanced_gemini_analysis_YYYYMMDD_HHMMSS.md`
- **内容**: AI 驱动的深度股票分析，包含投资建议和风险评估
- **格式**: Markdown

### 2. 后排股票分析报告
- **文件名**: `bottom_stocks_analysis_YYYYMMDD_HHMMSS.md`
- **内容**: 后排股票上升潜力分析，包含评分和推荐
- **格式**: Markdown

### 3. 后排股票数据文件
- **文件名**: `bottom_stocks_data_YYYYMMDD_HHMMSS.json`
- **内容**: 结构化的后排股票数据和统计信息
- **格式**: JSON

### 4. 趋势分析报告
- **文件名**: `trends_analysis_YYYYMMDD_HHMMSS.md`
- **内容**: 股票趋势深度分析，包含相关性分析和行业概念分析
- **格式**: Markdown

## 🔧 配置选项

### 环境变量
```bash
export GEMINI_API_KEY="your_api_key_here"
```

### 命令行参数
- `--input_dir`: 输入数据目录 (默认: `output`)
- `--output_dir`: 输出报告目录 (默认: `reports`)
- `--api_key`: Gemini API 密钥 (可选，用于 AI 分析)

## 📝 示例用法

### 完整分析流程
```bash
# 1. 设置 API 密钥
export GEMINI_API_KEY="your_api_key_here"

# 2. 运行所有分析
python3 run_all_analysis.py --input_dir output --output_dir daily_reports

# 3. 查看结果
ls -la daily_reports/
```

### 只运行本地分析 (无需API密钥)
```bash
# 运行后排股票分析和趋势分析
python3 bottom_stock_analyzer.py --input_dir output --output_dir reports
python3 analyze_trends.py --output_dir reports
```

## 🎯 优势

1. **集中管理**: 所有报告文件在同一目录，便于查找和管理
2. **时间戳**: 文件名包含时间戳，避免覆盖，便于版本管理
3. **灵活配置**: 支持自定义输出目录，适应不同需求
4. **统一接口**: 所有脚本使用相同的参数格式
5. **批量运行**: 提供统一脚本一次运行所有分析

## 🔄 迁移指南

如果你之前使用的是分散的输出目录，可以：

1. **清理旧文件** (可选):
   ```bash
   # 备份旧文件
   mkdir -p backup
   mv analysis_reports/ backup/ 2>/dev/null || true
   mv my_analysis/ backup/ 2>/dev/null || true
   mv test_reports/ backup/ 2>/dev/null || true
   ```

2. **使用新的统一目录**:
   ```bash
   # 创建新的报告目录
   mkdir -p reports
   
   # 运行分析
   python3 run_all_analysis.py --input_dir output --output_dir reports
   ```

## 📞 支持

如果遇到问题，请检查：
1. 输入目录是否存在且包含有效数据
2. 是否有写入输出目录的权限
3. Python 依赖是否已安装 (`pip install pandas requests`)
4. Gemini API 密钥是否有效 (如果使用 AI 分析)
